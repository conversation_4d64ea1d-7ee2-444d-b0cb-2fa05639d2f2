import { mutationOptions, queryOptions } from "@tanstack/react-query";
import ky from "ky";
import { envs } from "@/lib/constants/envs";
import { getQueryClient } from "@/app/providers";
import { endpoints } from "@/lib/constants/endpoints";

export const inboundTrunkQueryOptions = queryOptions({
  queryKey: ["listSipInboundTrunk"],
  queryFn: async () => {
    console.log("Fetching inbound trunks from:", `${envs.appUrl}${endpoints.inboundTrunk}`);
    return await ky.get(`${envs.appUrl}${endpoints.inboundTrunk}`).json<
      {
        sipTrunkId: string;
        name: string;
        metadata: string;
        address: string;
        transport: string;
        numbers: string[];
        authUsername: string;
        authPassword: string;
        headers: object;
        headersToAttributes: object;
        attributesToHeaders: object;
        includeHeaders: string;
        mediaEncryption: string;
        destinationCountry: string;
      }[]
    >();
  },
});

export const outboundTrunkQueryOptions = queryOptions({
  queryKey: ["listSipOutboundTrunk"],
  queryFn: async () => {
    return await ky.get(`${envs.appUrl}${endpoints.outboundTrunk}`).json<
      {
        sipTrunkId: string;
        name: string;
        metadata: string;
        address: string;
        transport: string;
        numbers: string[];
        authUsername: string;
        authPassword: string;
        headers: object;
        headersToAttributes: object;
        attributesToHeaders: object;
        includeHeaders: string;
        mediaEncryption: string;
        destinationCountry: string;
      }[]
    >();
  },
});

export const createInboundTrunkMutationOptions = mutationOptions({
  mutationFn: async (data: { name: string; numbers: string[]; authUsername?: string; authPassword?: string }) => {
    return await ky.post(`${envs.appUrl}${endpoints.inboundTrunk}`, { json: data }).json();
  },
  onSuccess: () => {
    getQueryClient().invalidateQueries({ queryKey: inboundTrunkQueryOptions.queryKey });
  },
});

export const createOutboundTrunkMutationOptions = mutationOptions({
  mutationFn: async (data: { name: string; numbers: string[]; address: string; authUsername?: string; authPassword?: string }) => {
    return await ky.post(`${envs.appUrl}${endpoints.outboundTrunk}`, { json: data }).json();
  },
  onSuccess: () => {
    getQueryClient().invalidateQueries({ queryKey: outboundTrunkQueryOptions.queryKey });
  },
});

export const deleteTrunkMutationOptions = mutationOptions({
  mutationFn: async (data: { sipTrunkId: string }) => {
    return await ky.delete(`${envs.appUrl}${endpoints.trunk}/${data.sipTrunkId}`).json();
  },
  onSuccess: () => {
    const queryClient = getQueryClient();
    queryClient.invalidateQueries({ queryKey: inboundTrunkQueryOptions.queryKey });
    queryClient.invalidateQueries({ queryKey: outboundTrunkQueryOptions.queryKey });
  },
});
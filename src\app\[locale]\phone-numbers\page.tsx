import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from "@tanstack/react-query";
import AddPhoneNumberPopover from "@/components/pages/phone-numbers-page/add-phone-number-popover";
import PhoneNumbersTableClient from "@/components/pages/phone-numbers-page/phone-numbers-table-client";
import { inboundTrunkQueryOptions, outboundTrunkQueryOptions } from "@/query-options/sip-client";
import PhoneNumbersPageTitle from "@/components/pages/phone-numbers-page/phone-numbers-page-title";

import React from "react";

export default async function PhoneNumbersPage() {
  const queryClient = new QueryClient();

  await queryClient.fetchQuery(inboundTrunkQueryOptions);
  await queryClient.fetchQuery(outboundTrunkQueryOptions);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <div className="flex flex-col w-full space-y-4 p-4 md:p-8 pt-6 pb-16 overflow-hidden max-h-[100vh]">
        <div className="flex items-center gap-3">
          <h2 className="text-xl font-bold tracking-tight">
            <PhoneNumbersPageTitle />
          </h2>
          <AddPhoneNumberPopover />
        </div>
        
        <PhoneNumbersTableClient />
      </div>
    </HydrationBoundary>
  );
}
// import {
//   dehydrate,
//   HydrationBoundary,
//   QueryClient,
// } from "@tanstack/react-query";
// import { inboundTrunkQueryOptions } from "@/query-options/sip-client";
// import InnerComponent from "./inner-component";

// export default async function PhoneNumbersPage() {
//   const queryClient = new QueryClient();

//   await queryClient.fetchQuery(inboundTrunkQueryOptions);

//   return (
//     <HydrationBoundary state={dehydrate(queryClient)}>
//       <InnerComponent />
//     </HydrationBoundary>
//   );
// }
